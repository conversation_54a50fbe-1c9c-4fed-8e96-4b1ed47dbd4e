// TaskFlow.ApiService/Program.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using TaskFlow.ApiService.Data;
using TaskFlow.ApiService.Models; // Assuming your models are here

var builder = WebApplication.CreateBuilder(args);

// Add service defaults, Aspire components, and Swagger/OpenAPI support
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "TaskFlow API", Version = "v1" });
});
builder.AddServiceDefaults();

// Add services to the container.
builder.Services.AddProblemDetails();

// Configure SQLite
var connectionString = builder.Configuration.GetConnectionString("sqlite") ?? "Data Source=taskflow.db";
builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseSqlite(connectionString));

// This is crucial for OpenAPI document generation with Minimal APIs
builder.Services.AddEndpointsApiExplorer();

// This registers the OpenAPI document generator
// No need for AddSwaggerGen() anymore.
// For .NET 8+ (and presumably .NET 9), AddOpenApi is often implicitly
// handled when using AddEndpointsApiExplorer and MapOpenApi,
// but you can add it explicitly if needed or for clarity:
// builder.Services.AddOpenApi(); // Often not strictly necessary if MapOpenApi is used.

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    // Apply migrations at startup (for development convenience)
    using (var scope = app.Services.CreateScope())
    {
        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        dbContext.Database.Migrate();
    }
}

app.UseExceptionHandler();
app.UseHttpsRedirection();
app.MapDefaultEndpoints(); // For health checks etc.

// This maps the endpoint that serves the OpenAPI JSON document.
// Typically, it will be available at /openapi/v1.json
app.MapOpenApi();

// --- Your API Endpoints (Projects, Tasks, Sprints) ---
// (Keep all the app.MapGroup, .MapGet, .MapPost, etc. calls as before)

var projectsApi = app.MapGroup("/api/projects").WithTags("Projects"); // Adding tags is good practice

// Project Endpoints
projectsApi.MapGet("/", async (AppDbContext db) => await db.Projects.ToListAsync());

projectsApi.MapGet("/{id}", async (int id, AppDbContext db) =>
    await db.Projects.Include(p => p.Tasks).Include(p => p.Sprints).FirstOrDefaultAsync(p => p.Id == id)
        is Project project
            ? Results.Ok(project)
            : Results.NotFound());

projectsApi.MapPost("/", async (Project project, AppDbContext db) =>
{
    project.CreatedAt = DateTime.UtcNow;
    db.Projects.Add(project);
    await db.SaveChangesAsync();
    return Results.Created($"/api/projects/{project.Id}", project);
});

projectsApi.MapPut("/{id}", async (int id, Project inputProject, AppDbContext db) =>
{
    var project = await db.Projects.FindAsync(id);
    if (project is null) return Results.NotFound();

    project.Name = inputProject.Name;
    project.Description = inputProject.Description;
    await db.SaveChangesAsync();
    return Results.NoContent();
});

projectsApi.MapDelete("/{id}", async (int id, AppDbContext db) =>
{
    if (await db.Projects.FindAsync(id) is Project project)
    {
        db.Projects.Remove(project);
        await db.SaveChangesAsync();
        return Results.Ok(project);
    }
    return Results.NotFound();
});


var tasksApi = app.MapGroup("/api/tasks").WithTags("Tasks");

// Task Endpoints
tasksApi.MapGet("/project/{projectId}", async (int projectId, AppDbContext db) =>
    await db.TaskItems.Where(t => t.ProjectId == projectId).ToListAsync());

tasksApi.MapGet("/{id}", async (int id, AppDbContext db) =>
    await db.TaskItems.FindAsync(id)
        is TaskItem task
            ? Results.Ok(task)
            : Results.NotFound());

tasksApi.MapPost("/", async (TaskItem task, AppDbContext db) =>
{
    task.CreatedAt = DateTime.UtcNow;
    task.UpdatedAt = DateTime.UtcNow;
    db.TaskItems.Add(task);
    await db.SaveChangesAsync();
    return Results.Created($"/api/tasks/{task.Id}", task);
});

tasksApi.MapPut("/{id}", async (int id, TaskItem inputTask, AppDbContext db) =>
{
    var task = await db.TaskItems.FindAsync(id);
    if (task is null) return Results.NotFound();

    task.Title = inputTask.Title;
    task.Description = inputTask.Description;
    task.Status = inputTask.Status;
    task.Priority = inputTask.Priority;
    task.DueDate = inputTask.DueDate;
    task.SprintId = inputTask.SprintId;
    task.ProjectId = inputTask.ProjectId;
    task.UpdatedAt = DateTime.UtcNow;

    await db.SaveChangesAsync();
    return Results.Ok(task);
});

tasksApi.MapPut("/{id}/status", async (int id, TaskFlow.ApiService.Models.TaskStatus newStatus, AppDbContext db) =>
{
    var task = await db.TaskItems.FindAsync(id);
    if (task is null) return Results.NotFound();

    task.Status = newStatus;
    task.UpdatedAt = DateTime.UtcNow;
    await db.SaveChangesAsync();
    return Results.Ok(task);
});


tasksApi.MapDelete("/{id}", async (int id, AppDbContext db) =>
{
    if (await db.TaskItems.FindAsync(id) is TaskItem task)
    {
        db.TaskItems.Remove(task);
        await db.SaveChangesAsync();
        return Results.Ok(task);
    }
    return Results.NotFound();
});

var sprintsApi = app.MapGroup("/api/sprints").WithTags("Sprints");

sprintsApi.MapGet("/project/{projectId}", async (int projectId, AppDbContext db) =>
    await db.Sprints.Where(s => s.ProjectId == projectId).Include(s => s.Tasks).ToListAsync());

sprintsApi.MapPost("/", async (Sprint sprint, AppDbContext db) =>
{
    db.Sprints.Add(sprint);
    await db.SaveChangesAsync();
    return Results.Created($"/api/sprints/{sprint.Id}", sprint);
});
// Add GetById, Put, Delete for Sprints as needed

// --- End of API Endpoints ---

app.Run();
