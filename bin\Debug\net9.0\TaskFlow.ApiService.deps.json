{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"TaskFlow.ApiService/1.0.0": {"dependencies": {"Microsoft.AspNetCore.OpenApi": "9.0.0", "Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.EntityFrameworkCore.Sqlite": "9.0.5", "TaskFlow.ServiceDefaults": "1.0.0"}, "runtime": {"TaskFlow.ApiService.dll": {}}}, "Google.Protobuf/3.22.5": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.22.5.0", "fileVersion": "3.22.5.0"}}}, "Grpc.Core.Api/2.52.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.52.0.0"}}}, "Grpc.Net.Client/2.52.0": {"dependencies": {"Grpc.Net.Common": "2.52.0"}, "runtime": {"lib/net7.0/Grpc.Net.Client.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.52.0.0"}}}, "Grpc.Net.Common/2.52.0": {"dependencies": {"Grpc.Core.Api": "2.52.0"}, "runtime": {"lib/net7.0/Grpc.Net.Common.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.52.0.0"}}}, "Microsoft.AspNetCore.OpenApi/9.0.0": {"dependencies": {"Microsoft.OpenApi": "1.6.17"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.Data.Sqlite.Core/9.0.5": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net8.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.5", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.5": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.10", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.5"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.5": {"dependencies": {"Microsoft.Data.Sqlite.Core": "9.0.5", "Microsoft.EntityFrameworkCore.Relational": "9.0.5", "Microsoft.Extensions.Caching.Memory": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyModel": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "SQLitePCLRaw.core": "2.1.10", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21604"}}}, "Microsoft.Extensions.AmbientMetadata.Application/9.5.0": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Hosting.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.AmbientMetadata.Application.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Compliance.Abstractions/9.5.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.ObjectPool": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Compliance.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Configuration/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.5.0": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.AutoActivation.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.DependencyModel/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.5.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.ExceptionSummarization.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.5", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Http/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Diagnostics": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Http.Diagnostics/9.5.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "9.5.0", "Microsoft.Extensions.Http": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5", "Microsoft.Extensions.Telemetry": "9.5.0", "System.IO.Pipelines": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Http.Resilience/9.5.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.Http.Diagnostics": "9.5.0", "Microsoft.Extensions.ObjectPool": "9.0.5", "Microsoft.Extensions.Resilience": "9.5.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Http.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Logging/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.5", "Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Logging": "9.0.5", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.ObjectPool/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.22904"}}}, "Microsoft.Extensions.Options/9.0.5": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.5", "Microsoft.Extensions.Configuration.Binder": "9.0.5", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.5", "Microsoft.Extensions.Options": "9.0.5", "Microsoft.Extensions.Primitives": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Primitives/9.0.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "Microsoft.Extensions.Resilience/9.5.0": {"dependencies": {"Microsoft.Extensions.Diagnostics": "9.0.5", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "9.5.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.5", "Microsoft.Extensions.Telemetry.Abstractions": "9.5.0", "Polly.Extensions": "8.4.2", "Polly.RateLimiting": "8.4.2"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Resilience.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.ServiceDiscovery/9.3.0": {"dependencies": {"Microsoft.Extensions.ServiceDiscovery.Abstractions": "9.3.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.dll": {"assemblyVersion": "9.3.0.0", "fileVersion": "9.300.25.26520"}}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.3.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.ServiceDiscovery.Abstractions.dll": {"assemblyVersion": "9.3.0.0", "fileVersion": "9.300.25.26520"}}}, "Microsoft.Extensions.Telemetry/9.5.0": {"dependencies": {"Microsoft.Extensions.AmbientMetadata.Application": "9.5.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "9.5.0", "Microsoft.Extensions.Logging.Configuration": "9.0.5", "Microsoft.Extensions.ObjectPool": "9.0.5", "Microsoft.Extensions.Telemetry.Abstractions": "9.5.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.Extensions.Telemetry.Abstractions/9.5.0": {"dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "9.5.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.5", "Microsoft.Extensions.ObjectPool": "9.0.5", "Microsoft.Extensions.Options": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Telemetry.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.500.25.26209"}}}, "Microsoft.OpenApi/1.6.17": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.6.17.0", "fileVersion": "1.6.17.0"}}}, "OpenTelemetry/1.9.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.dll": {"assemblyVersion": "*******", "fileVersion": "1.9.0.1312"}}}, "OpenTelemetry.Api/1.12.0": {"runtime": {"lib/net9.0/OpenTelemetry.Api.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"dependencies": {"OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net9.0/OpenTelemetry.Api.ProviderBuilderExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "1.12.0.1644"}}}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.9.0": {"dependencies": {"Google.Protobuf": "3.22.5", "Grpc.Net.Client": "2.52.0", "OpenTelemetry": "1.9.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Exporter.OpenTelemetryProtocol.dll": {"assemblyVersion": "*******", "fileVersion": "1.9.0.1312"}}}, "OpenTelemetry.Extensions.Hosting/1.9.0": {"dependencies": {"OpenTelemetry": "1.9.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "1.9.0.1312"}}}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.AspNetCore.dll": {"assemblyVersion": "1.12.0.490", "fileVersion": "1.12.0.490"}}}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.12.0"}, "runtime": {"lib/net8.0/OpenTelemetry.Instrumentation.Http.dll": {"assemblyVersion": "1.12.0.493", "fileVersion": "1.12.0.493"}}}, "OpenTelemetry.Instrumentation.Runtime/1.9.0": {"dependencies": {"OpenTelemetry.Api": "1.12.0"}, "runtime": {"lib/net6.0/OpenTelemetry.Instrumentation.Runtime.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Polly.Core/8.4.2": {"runtime": {"lib/net8.0/Polly.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.Extensions/8.4.2": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "Polly.RateLimiting/8.4.2": {"dependencies": {"Polly.Core": "8.4.2"}, "runtime": {"lib/net8.0/Polly.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.4.2.3950"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.10", "SQLitePCLRaw.provider.e_sqlite3": "2.1.10"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.core/2.1.10": {"runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net9.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-s390x/native/libe_sqlite3.so": {"rid": "linux-musl-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"dependencies": {"SQLitePCLRaw.core": "2.1.10"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.10.2445", "fileVersion": "2.1.10.2445"}}}, "System.IO.Pipelines/9.0.5": {"runtime": {"lib/net9.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "System.Text.Json/9.0.5": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.525.21509"}}}, "TaskFlow.ServiceDefaults/1.0.0": {"dependencies": {"Microsoft.Extensions.Http.Resilience": "9.5.0", "Microsoft.Extensions.ServiceDiscovery": "9.3.0", "OpenTelemetry.Exporter.OpenTelemetryProtocol": "1.9.0", "OpenTelemetry.Extensions.Hosting": "1.9.0", "OpenTelemetry.Instrumentation.AspNetCore": "1.12.0", "OpenTelemetry.Instrumentation.Http": "1.12.0", "OpenTelemetry.Instrumentation.Runtime": "1.9.0"}, "runtime": {"TaskFlow.ServiceDefaults.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"TaskFlow.ApiService/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Google.Protobuf/3.22.5": {"type": "package", "serviceable": true, "sha512": "sha512-tTMtDZPbLxJew8pk7NBdqhLqC4OipfkZdwPuCEUNr2AoDo1siUGcxFqJK0wDewTL8ge5Cjrb16CToMPxBUHMGA==", "path": "google.protobuf/3.22.5", "hashPath": "google.protobuf.3.22.5.nupkg.sha512"}, "Grpc.Core.Api/2.52.0": {"type": "package", "serviceable": true, "sha512": "sha512-SQiPyBczG4vKPmI6Fd+O58GcxxDSFr6nfRAJuBDUNj+PgdokhjWJvZE/La1c09AkL2FVm/jrDloG89nkzmVF7A==", "path": "grpc.core.api/2.52.0", "hashPath": "grpc.core.api.2.52.0.nupkg.sha512"}, "Grpc.Net.Client/2.52.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWVH9g/Nnjz40ni//2S8UIOyEmhueQREoZIkD0zKHEPqLxXcNlbp4eebXIOicZtkwDSx0TFz9NpkbecEDn6rBw==", "path": "grpc.net.client/2.52.0", "hashPath": "grpc.net.client.2.52.0.nupkg.sha512"}, "Grpc.Net.Common/2.52.0": {"type": "package", "serviceable": true, "sha512": "sha512-di9qzpdx525IxumZdYmu6sG2y/gXJyYeZ1ruFUzB9BJ1nj4kU1/dTAioNCMt1VLRvNVDqh8S8B1oBdKhHJ4xRg==", "path": "grpc.net.common/2.52.0", "hashPath": "grpc.net.common.2.52.0.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FqUK5j1EOPNuFT7IafltZQ3cakqhSwVzH5ZW1MhZDe4pPXs9sJ2M5jom1Omsu+mwF2tNKKlRAzLRHQTZzbd+6Q==", "path": "microsoft.aspnetcore.openapi/9.0.0", "hashPath": "microsoft.aspnetcore.openapi.9.0.0.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cP5eBSqra4Ae80X72g0h2N+jdrA4BgoMQmz9JaQmKAEXUHw9N21DPIBqIyMjOo2fK9ISiGytlAOxBAJf1hEvqg==", "path": "microsoft.data.sqlite.core/9.0.5", "hashPath": "microsoft.data.sqlite.core.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-TeCtb/vc+jxvgkVAqeJlZKOoG5w/w8AigWQQyOmeJsJ7+0SkONX8bqEV/wB+ojnT0sXuJrrfXQOEC3ws6asEng==", "path": "microsoft.entityframeworkcore/9.0.5", "hashPath": "microsoft.entityframeworkcore.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-81fGyIibhGc4rq4ZxmVZE/1CFSvGMQOZqdRyCBLKz/Hb8eE973dmSfcdXpXhQ/5f+nbax4VGkWhwPGxWUNWaCQ==", "path": "microsoft.entityframeworkcore.abstractions/9.0.5", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-kWRrD69qCXo7lahPZPt7C127UfK0I024laFZEDMfT3JbALB1EWneFvq1utWM0cNKPFuYis1E1oaYTuRGI/9inQ==", "path": "microsoft.entityframeworkcore.analyzers/9.0.5", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6eErbrZFd9yNnncemtDdmHZ3KC792OQCIYITuMsjK2oh4CLzlYo8mzNsozgUzQ+utHnne11/3eV8zMWbYF5Puw==", "path": "microsoft.entityframeworkcore.relational/9.0.5", "hashPath": "microsoft.entityframeworkcore.relational.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-dy9e3TUiU9COlFTyut6e12bZALM25PDskd6Kk10gbS3rAPYsuaKTkgq3mHDIQOR2bb3WEX7cdNpNF1+r2BIBMg==", "path": "microsoft.entityframeworkcore.sqlite/9.0.5", "hashPath": "microsoft.entityframeworkcore.sqlite.9.0.5.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-YU11QeQz53xKvr9hV9XABU9nwMMPOJFYuLB5bWgPMoE73ibiprksFzpnWhifRQu0c35jwVTj7kxHIAi/800CXA==", "path": "microsoft.entityframeworkcore.sqlite.core/9.0.5", "hashPath": "microsoft.entityframeworkcore.sqlite.core.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.AmbientMetadata.Application/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-GHgq9tfncuDSaPtwd1od9It3omPuq5PmHDkTWC91VRy75SWvvT7AX+zyDEurp1+oYgLEObQ6PICv+hZvht8dig==", "path": "microsoft.extensions.ambientmetadata.application/9.5.0", "hashPath": "microsoft.extensions.ambientmetadata.application.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-RV6wOTvH5BeVRs6cvxFuaV1ut05Dklpvq19XRO1JxAayfLWYIEP7K94aamY0iSUhoehWk1X5H6gMcbZkHuBjew==", "path": "microsoft.extensions.caching.abstractions/9.0.5", "hashPath": "microsoft.extensions.caching.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-qDmoAzIUBup5KZG1Abv51ifbHMCWFnaXbt05l+Sd92mLOpF9OwHOuoxu3XhzXaPGfq0Ns3pv1df5l8zuKjFgGw==", "path": "microsoft.extensions.caching.memory/9.0.5", "hashPath": "microsoft.extensions.caching.memory.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Compliance.Abstractions/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-jK7bWPhu60GvcVSqMXOdV6ZLOP5rnwvmlqSD2E5fTkAXwoGYUV/5U3tQbvlZtOpeXTu509eg2VEb+l66d7dtSg==", "path": "microsoft.extensions.compliance.abstractions/9.5.0", "hashPath": "microsoft.extensions.compliance.abstractions.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-uYXLg2Gt8KUH5nT3u+TBpg9VrRcN5+2zPmIjqEHR4kOoBwsbtMDncEJw9HiLvZqGgIo2TR4oraibAoy5hXn2bQ==", "path": "microsoft.extensions.configuration/9.0.5", "hashPath": "microsoft.extensions.configuration.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-ew0G6gIznnyAkbIa67wXspkDFcVektjN3xaDAfBDIPbWph+rbuGaaohFxUSGw28ht7wdcWtTtElKnzfkcDDbOQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.5", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-7pQ4Tkyofm8DFWFhqn9ZmG8qSAC2VitWleATj5qob9V9KtoxCVdwRtmiVl/ha3WAgjkEfW++JLWXox9MJwMgkg==", "path": "microsoft.extensions.configuration.binder/9.0.5", "hashPath": "microsoft.extensions.configuration.binder.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-N1Mn0T/tUBPoLL+Fzsp+VCEtneUhhxc1//Dx3BeuQ8AX+XrMlYCfnp2zgpEXnTCB7053CLdiqVWPZ7mEX6MPjg==", "path": "microsoft.extensions.dependencyinjection/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-cjnRtsEAzU73aN6W7vkWy8Phj5t3Xm78HSqgrbh/O4Q9SK/yN73wZVa21QQY6amSLQRQ/M8N+koGnY6PuvKQsw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.5", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.AutoActivation/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-JT0VpQl0yrkqdQB8IrN3IqI5frfH8BzuHSsHbNU0Y1qbG/F+gF6A7rBAZTR4NfxQHTwTrO7tnrrVixEmBQ/PyQ==", "path": "microsoft.extensions.dependencyinjection.autoactivation/9.5.0", "hashPath": "microsoft.extensions.dependencyinjection.autoactivation.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-+jdJ9Vz+5Ia21l3KjahtmeHCIgQ7urfkdcJPxSfeqB40Jqryi27Lt4fKBmKyvc0YrfTUJ0cEB7QmoQRlU8FH0g==", "path": "microsoft.extensions.dependencymodel/9.0.5", "hashPath": "microsoft.extensions.dependencymodel.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-fRiUjmhm9e4vMp6WEO9MgWNxVtWSr4Pcgh1W4DyJIr8bRANlZz9JU7uicf7ShzMspDxo/9Ejo9zJ6qQZY0IhVw==", "path": "microsoft.extensions.diagnostics/9.0.5", "hashPath": "microsoft.extensions.diagnostics.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6YfTcULCYREMTqtk+s3UiszsFV2xN2FXtxdQpurmQJY9Cp/QGiM4MTKfJKUo7AzdLuzjOKKMWjQITmvtK7AsUg==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.5", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QKLs7fG05WqLyp5zOGENZ18XZzVJ2TCw7WTnsQcDwQqA34YOev2vBmIk4FwtPqPieGdqQhzcN032nZkKacIrdQ==", "path": "microsoft.extensions.diagnostics.exceptionsummarization/9.5.0", "hashPath": "microsoft.extensions.diagnostics.exceptionsummarization.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-LLm+e8lvD+jOI+blHRSxPqywPaohOTNcVzQv548R1UpkEiNB2D+zf3RrqxBdB1LDPicRMTnfiaKJovxF8oX1bQ==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.5", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-3GA/dxqkP6yFe18qYRgtKYuN2onC8NfhlpNN21jptkVKk7olqBTkdT49oL0pSEz2SptRsux7LocCU7+alGnEag==", "path": "microsoft.extensions.hosting.abstractions/9.0.5", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-6vbo3XjyEc+w/kv/Dkfv9NA7iSdIdX5dlU9Shk3wJJ0fiZpCVzVW5FJtNoIePX5hS0ENNpHPClq/qtq06yM4FQ==", "path": "microsoft.extensions.http/9.0.5", "hashPath": "microsoft.extensions.http.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Http.Diagnostics/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-K5VI2QA3Y2s675IyyhA34xKkUK0XZKgXU0ddXOkuCuHiEWLKs/McNVGP1Qw5B5UEGxrei7aj20Ssm+9JJ6yTlg==", "path": "microsoft.extensions.http.diagnostics/9.5.0", "hashPath": "microsoft.extensions.http.diagnostics.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Resilience/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-6+D5dTm6t+Rg93ODzQ/obWuUxl+YPEm6bU4lz6dA2lxPjJmqbsUKP3l2bXlMcahj3EFj9n4gpRD7fzF46q+ecQ==", "path": "microsoft.extensions.http.resilience/9.5.0", "hashPath": "microsoft.extensions.http.resilience.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rQU61lrgvpE/UgcAd4E56HPxUIkX/VUQCxWmwDTLLVeuwRDYTL0q/FLGfAW17cGTKyCh7ywYAEnY3sTEvURsfg==", "path": "microsoft.extensions.logging/9.0.5", "hashPath": "microsoft.extensions.logging.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-pP1PADCrIxMYJXxFmTVbAgEU7GVpjK5i0/tyfU9DiE0oXQy3JWQaOVgCkrCiePLgS8b5sghM3Fau3EeHiVWbCg==", "path": "microsoft.extensions.logging.abstractions/9.0.5", "hashPath": "microsoft.extensions.logging.abstractions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-WgYTJ1/dxdzqaYYMrgC6cZXJVmaoxUmWgsvR9Kg5ZARpy0LMw7fZIZMIiVuaxhItwwFIW0ruhAN+Er2/oVZgmQ==", "path": "microsoft.extensions.logging.configuration/9.0.5", "hashPath": "microsoft.extensions.logging.configuration.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-C0VDKwSwNfc3YCLuno6Dip0un9LFmvuSvhpCC4ckpz6nrOmiM5JSJspQiY1dGCDRXJKFeZxa2hDpCLRL8WiBhw==", "path": "microsoft.extensions.objectpool/9.0.5", "hashPath": "microsoft.extensions.objectpool.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-vPdJQU8YLOUSSK8NL0RmwcXJr2E0w8xH559PGQl4JYsglgilZr9LZnqV2zdgk+XR05+kuvhBEZKoDVd46o7NqA==", "path": "microsoft.extensions.options/9.0.5", "hashPath": "microsoft.extensions.options.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-CJbAVdovKPFh2FoKxesu20odRVSbL/vtvzzObnG+5u38sOfzRS2Ncy25id0TjYUGQzMhNnJUHgTUzTMDl/3c9g==", "path": "microsoft.extensions.options.configurationextensions/9.0.5", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-b4OAv1qE1C9aM+ShWJu3rlo/WjDwa/I30aIPXqDWSKXTtKl1Wwh6BZn+glH5HndGVVn3C6ZAPQj5nv7/7HJNBQ==", "path": "microsoft.extensions.primitives/9.0.5", "hashPath": "microsoft.extensions.primitives.9.0.5.nupkg.sha512"}, "Microsoft.Extensions.Resilience/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-KoL2rlNW/l+CWfiIFb8ZjBaqgY5DSnla5ZIxBvt5BdkGZ+xoe7ZMEw62FeQ+yot053VEPbPpZ/iTnX3zZ6kB3Q==", "path": "microsoft.extensions.resilience/9.5.0", "hashPath": "microsoft.extensions.resilience.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rYDGIzU9XCf9Gg4DLzJ2vjlCiXbEXXJbIbFbGZQrKVppEV84sq2vkX3zH9HQIa5IVuqPzxpoCfIYWeG1x8Ipcg==", "path": "microsoft.extensions.servicediscovery/9.3.0", "hashPath": "microsoft.extensions.servicediscovery.9.3.0.nupkg.sha512"}, "Microsoft.Extensions.ServiceDiscovery.Abstractions/9.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-EK+R8ZpSQdfpCPtGQoq8FJaWHdWfpTCtudTIf05P4xn/9ni4+/b3FCaeT+Xk2oT/DraEymJN4X9zxwNcBEYm6A==", "path": "microsoft.extensions.servicediscovery.abstractions/9.3.0", "hashPath": "microsoft.extensions.servicediscovery.abstractions.9.3.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-oaLcdlYgsEl6YAUdwrBHFakGgkHxifOTZQXu6T57OvRm0DByJhn2YyeijUOM2iDIDUJObZhG5A9qL/JNehZydw==", "path": "microsoft.extensions.telemetry/9.5.0", "hashPath": "microsoft.extensions.telemetry.9.5.0.nupkg.sha512"}, "Microsoft.Extensions.Telemetry.Abstractions/9.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vYQAUcMkW06G973ocSzJT5WFWSN7J5l3yZTQF99nUhWP/pJ1SjYBxXXCk/7jqYnBjFJgsmdrP+JxqJio/EvyQQ==", "path": "microsoft.extensions.telemetry.abstractions/9.5.0", "hashPath": "microsoft.extensions.telemetry.abstractions.9.5.0.nupkg.sha512"}, "Microsoft.OpenApi/1.6.17": {"type": "package", "serviceable": true, "sha512": "sha512-Le+kehlmrlQfuDFUt1zZ2dVwrhFQtKREdKBo+rexOwaCoYP0/qpgT9tLxCsZjsgR5Itk1UKPcbgO+FyaNid/bA==", "path": "microsoft.openapi/1.6.17", "hashPath": "microsoft.openapi.1.6.17.nupkg.sha512"}, "OpenTelemetry/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-7scS6BUhwYeSXEDGhCxMSezmvyCoDU5kFQbmfyW9iVvVTcWhec+1KIN33/LOCdBXRkzt2y7+g03mkdAB0XZ9Fw==", "path": "opentelemetry/1.9.0", "hashPath": "opentelemetry.1.9.0.nupkg.sha512"}, "OpenTelemetry.Api/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xt0qldi+iE2szGrM3jAqzEMEJd48YBtqI6mge0+ArXTZg3aTpRmyhL6CKKl3bLioaFSSVbBpEbPin8u6Z46Yrw==", "path": "opentelemetry.api/1.12.0", "hashPath": "opentelemetry.api.1.12.0.nupkg.sha512"}, "OpenTelemetry.Api.ProviderBuilderExtensions/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-t6Vk1143BfiisCWYbRcyzkAuN6Aq5RkYtfOSMoqCIRMvtN9p1e1xzc0nWQ+fccNGOVgHn3aMK5xFn2+iWMcr8A==", "path": "opentelemetry.api.providerbuilderextensions/1.12.0", "hashPath": "opentelemetry.api.providerbuilderextensions.1.12.0.nupkg.sha512"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-qzFOP3V2eYIVbug3U4BJzzidHe9JhAJ42WZ/H8pUp/45Ry3MQQg/+e/ZieClJcxKnpbkXi7dUq1rpvuNp+yBYA==", "path": "opentelemetry.exporter.opentelemetryprotocol/1.9.0", "hashPath": "opentelemetry.exporter.opentelemetryprotocol.1.9.0.nupkg.sha512"}, "OpenTelemetry.Extensions.Hosting/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-QBQPrKDVCXxTBE+r8tgjmFNKKHi4sKyczmip2XGUcjy8kk3quUNhttnjiMqC4sU50Hemmn4i5752Co26pnKe3A==", "path": "opentelemetry.extensions.hosting/1.9.0", "hashPath": "opentelemetry.extensions.hosting.1.9.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.AspNetCore/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-r+Mzggd2P4N0Y34QIO6kakVPBOKFYSHnLkTrXXM+r37ABp+iaUvVUe+u/uxszsi5f7P5mrG0uYYaJ1QGHvzo3A==", "path": "opentelemetry.instrumentation.aspnetcore/1.12.0", "hashPath": "opentelemetry.instrumentation.aspnetcore.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Http/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-0rW+MbHgUQAdbvBtRxPYoQBosbNdWegL7cYkRlxq+KQ/VFyU8itt4pWTccmu1/FWmTgqJyT3LaujyDZoRrm8Yg==", "path": "opentelemetry.instrumentation.http/1.12.0", "hashPath": "opentelemetry.instrumentation.http.1.12.0.nupkg.sha512"}, "OpenTelemetry.Instrumentation.Runtime/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-6raJb9Pvi1CaBB59SX86Mr9NQiQbiv9ialO+cQKFRGCq3Bl2WC8cTTcbfGtaRX0quqWnZC/dK7xrXuOuYcwANA==", "path": "opentelemetry.instrumentation.runtime/1.9.0", "hashPath": "opentelemetry.instrumentation.runtime.1.9.0.nupkg.sha512"}, "Polly.Core/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-BpE2I6HBYYA5tF0Vn4eoQOGYTYIK1BlF5EXVgkWGn3mqUUjbXAr13J6fZVbp7Q3epRR8yshacBMlsHMhpOiV3g==", "path": "polly.core/8.4.2", "hashPath": "polly.core.8.4.2.nupkg.sha512"}, "Polly.Extensions/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-GZ9vRVmR0jV2JtZavt+pGUsQ1O1cuRKG7R7VOZI6ZDy9y6RNPvRvXK1tuS4ffUrv8L0FTea59oEuQzgS0R7zSA==", "path": "polly.extensions/8.4.2", "hashPath": "polly.extensions.8.4.2.nupkg.sha512"}, "Polly.RateLimiting/8.4.2": {"type": "package", "serviceable": true, "sha512": "sha512-ehTImQ/eUyO07VYW2WvwSmU9rRH200SKJ/3jku9rOkyWE0A2JxNFmAVms8dSn49QLSjmjFRRSgfNyOgr/2PSmA==", "path": "polly.ratelimiting/8.4.2", "hashPath": "polly.ratelimiting.8.4.2.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-UxWuisvZ3uVcVOLJQv7urM/JiQH+v3TmaJc1BLKl5Dxfm/nTzTUrqswCqg/INiYLi61AXnHo1M1JPmPqqLnAdg==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "path": "sqlitepclraw.core/2.1.10", "hashPath": "sqlitepclraw.core.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-mAr69tDbnf3QJpRy2nJz8Qdpebdil00fvycyByR58Cn9eARvR+UiG2Vzsp+4q1tV3ikwiYIjlXCQFc12GfebbA==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.10.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.10": {"type": "package", "serviceable": true, "sha512": "sha512-uZVTi02C1SxqzgT0HqTWatIbWGb40iIkfc3FpFCpE/r7g6K0PqzDUeefL6P6HPhDtc6BacN3yQysfzP7ks+wSQ==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.10", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.10.nupkg.sha512"}, "System.IO.Pipelines/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-5WXo+3MGcnYn54+1ojf+kRzKq1Q6sDUnovujNJ2ky1nl1/kP3+PMil9LPbFvZ2mkhvAGmQcY07G2sfHat/v0Fw==", "path": "system.io.pipelines/9.0.5", "hashPath": "system.io.pipelines.9.0.5.nupkg.sha512"}, "System.Text.Json/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "path": "system.text.json/9.0.5", "hashPath": "system.text.json.9.0.5.nupkg.sha512"}, "TaskFlow.ServiceDefaults/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}